<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SecureContract Pro - Favicon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1e40af;
            text-align: center;
        }
        .favicon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .favicon-item {
            text-align: center;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        .favicon-item h3 {
            margin: 10px 0 5px 0;
            color: #374151;
        }
        .favicon-item p {
            margin: 0;
            font-size: 12px;
            color: #6b7280;
        }
        canvas {
            border: 1px solid #d1d5db;
            margin: 5px;
        }
        .download-btn {
            background: #1e40af;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .download-btn:hover {
            background: #1d4ed8;
        }
        .instructions {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .instructions h3 {
            color: #1e40af;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 SecureContract Pro - Favicon Generator</h1>
        
        <div class="instructions">
            <h3>📋 Instructions:</h3>
            <ol>
                <li>The favicons will be generated automatically when you load this page</li>
                <li>Right-click on each canvas and "Save image as..." to download</li>
                <li>Save them with the exact filenames shown below</li>
                <li>Place all files in your <code>public/</code> directory</li>
            </ol>
        </div>

        <div class="favicon-preview">
            <div class="favicon-item">
                <h3>favicon.ico</h3>
                <canvas id="favicon32" width="32" height="32"></canvas>
                <p>32x32 - Main favicon</p>
                <button class="download-btn" onclick="downloadCanvas('favicon32', 'favicon-32x32.png')">Download PNG</button>
            </div>

            <div class="favicon-item">
                <h3>favicon-16x16.png</h3>
                <canvas id="favicon16" width="16" height="16"></canvas>
                <p>16x16 - Small favicon</p>
                <button class="download-btn" onclick="downloadCanvas('favicon16', 'favicon-16x16.png')">Download</button>
            </div>

            <div class="favicon-item">
                <h3>apple-touch-icon.png</h3>
                <canvas id="apple180" width="180" height="180"></canvas>
                <p>180x180 - iOS home screen</p>
                <button class="download-btn" onclick="downloadCanvas('apple180', 'apple-touch-icon.png')">Download</button>
            </div>

            <div class="favicon-item">
                <h3>android-chrome-192x192.png</h3>
                <canvas id="android192" width="192" height="192"></canvas>
                <p>192x192 - Android home screen</p>
                <button class="download-btn" onclick="downloadCanvas('android192', 'android-chrome-192x192.png')">Download</button>
            </div>
        </div>

        <div class="instructions">
            <h3>🔧 Next Steps:</h3>
            <ol>
                <li>Download all the favicon files using the buttons above</li>
                <li>Place them in your <code>public/</code> directory</li>
                <li>For favicon.ico, use an online converter to convert the 32x32 PNG to ICO format</li>
                <li>Test your favicon by refreshing your website</li>
            </ol>
        </div>
    </div>

    <script>
        function drawFavicon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 32; // Base design is 32x32
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Background circle
            ctx.fillStyle = '#1e40af';
            ctx.beginPath();
            ctx.arc(size/2, size/2, (size/2) - 2*scale, 0, 2 * Math.PI);
            ctx.fill();
            
            // Document rectangle
            const docLeft = 8 * scale;
            const docTop = 6 * scale;
            const docWidth = 12 * scale;
            const docHeight = 16 * scale;
            
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(docLeft, docTop, docWidth, docHeight);
            
            // Document lines
            ctx.strokeStyle = '#1e40af';
            ctx.lineWidth = Math.max(1, scale);
            
            const lineY1 = docTop + 3 * scale;
            const lineY2 = docTop + 6 * scale;
            const lineY3 = docTop + 9 * scale;
            
            ctx.beginPath();
            ctx.moveTo(docLeft + 2*scale, lineY1);
            ctx.lineTo(docLeft + docWidth - 2*scale, lineY1);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(docLeft + 2*scale, lineY2);
            ctx.lineTo(docLeft + docWidth - 2*scale, lineY2);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(docLeft + 2*scale, lineY3);
            ctx.lineTo(docLeft + docWidth - 4*scale, lineY3);
            ctx.stroke();
            
            // Security shield
            const shieldX = 16 * scale;
            const shieldY = 18 * scale;
            const shieldSize = 6 * scale;
            
            ctx.fillStyle = '#10b981';
            ctx.beginPath();
            ctx.moveTo(shieldX, shieldY - shieldSize/2);
            ctx.lineTo(shieldX + shieldSize/2, shieldY);
            ctx.lineTo(shieldX, shieldY + shieldSize/2);
            ctx.lineTo(shieldX - shieldSize/2, shieldY);
            ctx.closePath();
            ctx.fill();
            
            // Checkmark
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = Math.max(1, scale);
            ctx.beginPath();
            ctx.moveTo(shieldX - 2*scale, shieldY);
            ctx.lineTo(shieldX - scale, shieldY + scale);
            ctx.lineTo(shieldX + 2*scale, shieldY - 2*scale);
            ctx.stroke();
            
            // Signature line
            const sigY = 26 * scale;
            ctx.strokeStyle = '#6366f1';
            ctx.lineWidth = Math.max(2, 2*scale);
            ctx.beginPath();
            ctx.moveTo(docLeft + 2*scale, sigY);
            ctx.lineTo(docLeft + docWidth - 4*scale, sigY);
            ctx.stroke();
            
            // Signature dot
            ctx.fillStyle = '#6366f1';
            ctx.beginPath();
            ctx.arc(docLeft + docWidth - 2*scale, sigY, scale, 0, 2 * Math.PI);
            ctx.fill();
        }
        
        function downloadCanvas(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Generate all favicons when page loads
        window.onload = function() {
            drawFavicon(document.getElementById('favicon32'), 32);
            drawFavicon(document.getElementById('favicon16'), 16);
            drawFavicon(document.getElementById('apple180'), 180);
            drawFavicon(document.getElementById('android192'), 192);
        };
    </script>
</body>
</html>
