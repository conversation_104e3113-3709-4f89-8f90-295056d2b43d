# 🔧 Local Environment Setup Guide

This guide explains how to set up your local development environment with `.env.local` files that contain actual configuration values.

## 🎯 Overview

The project uses `.env.local` files for local development that:
- ✅ **Contain actual values** for development
- ✅ **Are ignored by git** (never committed)
- ✅ **Override example files** for local development
- ✅ **Include all necessary keys** for full functionality

## 🚀 Quick Setup

### Automated Setup (Recommended)

Run the interactive setup script:

```bash
./scripts/setup-local-env.sh
```

This script will:
1. Create `.env.local` (frontend) with your configuration
2. Create `backend/.env.local` (backend) with your configuration
3. Generate secure JWT and session secrets
4. Validate the setup

### Manual Setup

If you prefer to set up manually:

#### 1. Frontend `.env.local`

Create `.env.local` in the project root:

```bash
cp .env.local.example .env.local
# Edit .env.local with your actual values
```

#### 2. Backend `.env.local`

Create `backend/.env.local`:

```bash
cp backend/.env.local.example backend/.env.local
# Edit backend/.env.local with your actual values
```

## 📝 Configuration Values

### Frontend Configuration

Key variables to configure in `.env.local`:

```bash
# Backend Connection
VITE_API_URL=http://localhost:3001
VITE_BACKEND_URL=http://localhost:3001

# Solana Network
VITE_SOLANA_CLUSTER=devnet

# Analytics (Optional)
VITE_GA_TRACKING_ID=your_ga_tracking_id

# Development Features
VITE_ENABLE_DEBUG=true
VITE_ENABLE_DEV_TOOLS=true
```

### Backend Configuration

Key variables to configure in `backend/.env.local`:

```bash
# Server Configuration
NODE_ENV=development
PORT=3001

# Database
MONGO_URI=******************************************************************************
REDIS_URL=redis://localhost:6379

# Solana Configuration
SOLANA_CLUSTER=devnet
SOLANA_RPC_URL=https://api.devnet.solana.com

# Email Service
RESEND_API_KEY=your_actual_resend_api_key

# Security (Auto-generated by script)
JWT_SECRET=your_secure_jwt_secret
SESSION_SECRET=your_secure_session_secret

# CORS
CORS_ORIGIN=http://localhost:5173
FRONTEND_URL=http://localhost:5173
```

## 🔑 Required API Keys

### Resend API Key

1. Go to [Resend Dashboard](https://resend.com/api-keys)
2. Create a new API key
3. Add it to `backend/.env.local`:
   ```bash
   RESEND_API_KEY=re_your_actual_api_key_here
   ```

### Solana Platform Fee Keypair

Generate a development keypair:

```bash
# Generate new keypair
solana-keygen new --no-bip39-passphrase --outfile dev-keypair.json

# Get the private key array
solana-keygen pubkey dev-keypair.json
cat dev-keypair.json
```

Add the private key array to `backend/.env.local`:
```bash
PLATFORM_FEE_RECIPIENT_PRIVATE_KEY=[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64]
```

## 🔒 Security Features

### Git Ignore Protection

The `.env.local` files are protected by `.gitignore`:

```gitignore
# Environment variables and secrets
.env.local
backend/.env.local

# Local environment files (never commit these)
**/.env.local
**/env.local
.env.*.local
```

### Secure Secret Generation

The setup script automatically generates secure secrets:

```bash
# JWT Secret (64 characters)
JWT_SECRET=a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2

# Session Secret (64 characters)  
SESSION_SECRET=f2e1d0c9b8a7z6y5x4w3v2u1t0s9r8q7p6o5n4m3l2k1j0i9h8g7f6e5d4c3b2a1
```

## 🧪 Testing Your Setup

### 1. Validate Configuration

Run the validation script:

```bash
./scripts/test-environment.sh
```

### 2. Start Development Servers

```bash
# Start backend
cd backend && npm run dev

# Start frontend (in another terminal)
npm run dev
```

### 3. Check Environment Status

Visit the frontend and check:
- Network selector shows correct network
- Environment validator shows "Valid" status
- No errors in browser console

## 🔧 Troubleshooting

### Common Issues

#### 1. Missing API Keys
**Error**: `RESEND_API_KEY environment variable is required`
**Solution**: Add your Resend API key to `backend/.env.local`

#### 2. Database Connection Failed
**Error**: `Failed to connect to MongoDB`
**Solution**: 
- Start MongoDB: `docker-compose up -d mongodb`
- Check MONGO_URI in `backend/.env.local`

#### 3. Network Validation Errors
**Error**: Environment validation shows errors
**Solution**: 
- Check both `.env.local` files exist
- Verify VITE_SOLANA_CLUSTER matches SOLANA_CLUSTER
- Ensure all required variables are set

#### 4. CORS Errors
**Error**: `Access to fetch blocked by CORS policy`
**Solution**: 
- Check CORS_ORIGIN in `backend/.env.local`
- Ensure it matches your frontend URL

### Environment Variables Not Loading

If environment variables aren't loading:

1. **Check file names**: Must be exactly `.env.local`
2. **Check file location**: 
   - Frontend: `.env.local` in project root
   - Backend: `backend/.env.local`
3. **Restart servers**: Environment changes require restart
4. **Check syntax**: No spaces around `=` in env files

## 📁 File Structure

```
project-root/
├── .env.local                    # Frontend environment (git ignored)
├── .env.local.example           # Frontend template
├── backend/
│   ├── .env.local              # Backend environment (git ignored)
│   └── .env.local.example      # Backend template
├── scripts/
│   └── setup-local-env.sh      # Automated setup script
└── .gitignore                  # Protects .env.local files
```

## 🎯 Best Practices

### Development
- ✅ Use `.env.local` for actual values
- ✅ Keep `.env.example` files updated as templates
- ✅ Never commit `.env.local` files
- ✅ Use different API keys for different environments

### Security
- 🔒 Rotate API keys regularly
- 🔒 Use strong, unique secrets for each environment
- 🔒 Never share `.env.local` files
- 🔒 Use environment-specific database credentials

### Team Collaboration
- 📝 Document required environment variables
- 📝 Provide setup scripts for easy onboarding
- 📝 Keep example files up to date
- 📝 Share setup instructions with team members

## 🆘 Getting Help

If you encounter issues:

1. **Check the logs**: Look for specific error messages
2. **Validate environment**: Run `./scripts/test-environment.sh`
3. **Compare with examples**: Check `.env.example` files
4. **Restart services**: Environment changes need restart
5. **Check documentation**: Review this guide and network environment guide

---

**Remember**: `.env.local` files contain sensitive information and should never be committed to version control! 🔒
