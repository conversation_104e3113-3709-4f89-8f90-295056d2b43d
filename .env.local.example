# Frontend Environment Configuration - Local Development Template
# Copy this file to .env.local and fill in your actual values
# DO NOT COMMIT .env.local TO VERSION CONTROL

# Application Environment
NODE_ENV=development

# Frontend Configuration
VITE_API_URL=http://localhost:3001
VITE_BACKEND_URL=http://localhost:3001
VITE_SOLANA_CLUSTER=devnet

# Analytics (Optional for development)
VITE_GA_TRACKING_ID=

# Development Features
VITE_ENABLE_DEBUG=true
VITE_ENABLE_DEV_TOOLS=true

# Performance Monitoring (Optional)
VITE_ENABLE_PERFORMANCE_MONITORING=false

# Local Development URLs
VITE_FRONTEND_URL=http://localhost:5173
VITE_WEBSOCKET_URL=ws://localhost:3001

# Security Configuration
VITE_ENABLE_HTTPS=false

# Development Mode Flags
VITE_MOCK_WALLET=false
VITE_SKIP_WALLET_VALIDATION=false

# Local Storage Keys (for development)
VITE_STORAGE_PREFIX=securecontract_dev_

# API Configuration
VITE_API_TIMEOUT=30000
VITE_MAX_RETRIES=3

# Development Logging
VITE_LOG_LEVEL=debug
VITE_ENABLE_CONSOLE_LOGS=true
