{"version": 2, "name": "securecontract-pro", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/(.*)", "dest": "/index.html"}], "env": {"NODE_ENV": "production"}, "build": {"env": {"NODE_ENV": "production"}}, "functions": {"app/api/**/*.js": {"runtime": "nodejs18.x"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "rewrites": [{"source": "/sitemap.xml", "destination": "/sitemap.xml"}, {"source": "/robots.txt", "destination": "/robots.txt"}]}