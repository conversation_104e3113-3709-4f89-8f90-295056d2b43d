# Environment Configuration for Digital Contract Platform

# Application Environment
NODE_ENV=development
PORT=3001

# Database Configuration
MONGO_URI=******************************************************************************
REDIS_URL=redis://localhost:6379

# Solana Configuration
SOLANA_CLUSTER=devnet
SOLANA_RPC_URL=https://api.devnet.solana.com

# Platform Fee Configuration (Base58 encoded private key array)
# Generate a new keypair for production: solana-keygen new
PLATFORM_FEE_RECIPIENT_PRIVATE_KEY=[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64]

# IPFS Configuration (Optional)
IPFS_HOST=localhost
IPFS_PORT=5001
IPFS_PROTOCOL=http

# Email Configuration (Optional - for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# API Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain,image/jpeg,image/png

# Frontend Configuration
VITE_API_URL=http://localhost:3001
VITE_BACKEND_URL=http://localhost:3001
VITE_SOLANA_CLUSTER=devnet

# Network Environment Selection
# Supported values: devnet, testnet, mainnet-beta
# This determines which Solana network the application connects to
# devnet: Development network (free SOL via faucet)
# testnet: Testing network (free SOL via faucet)
# mainnet-beta: Production network (real SOL required)

# Security Configuration
CORS_ORIGIN=http://localhost:5173
SESSION_SECRET=your-session-secret-change-this-in-production

# Monitoring and Logging
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true

# Development Configuration
ENABLE_SWAGGER=true
ENABLE_DEBUG_ROUTES=true
