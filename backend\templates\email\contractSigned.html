<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contract Signed - Status Update</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .content {
            padding: 30px;
        }
        .status-update {
            background: #f0fff4;
            border-left: 4px solid #48bb78;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .status-update h3 {
            margin: 0 0 15px 0;
            color: #2d3748;
            font-size: 18px;
        }
        .detail-item {
            margin: 10px 0;
            display: flex;
            align-items: center;
        }
        .detail-label {
            font-weight: 600;
            color: #4a5568;
            min-width: 120px;
        }
        .detail-value {
            color: #2d3748;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
            transition: transform 0.2s;
        }
        .cta-button:hover {
            transform: translateY(-2px);
        }
        .footer {
            background: #f8fafc;
            padding: 20px 30px;
            text-align: center;
            color: #718096;
            font-size: 14px;
            border-top: 1px solid #e2e8f0;
        }
        .success-badge {
            background: #c6f6d5;
            color: #22543d;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            display: inline-block;
            margin: 10px 0;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .content {
                padding: 20px;
            }
            .header {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ Digital Contract Platform</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Contract Status Update</p>
        </div>
        
        <div class="content">
            <h2 style="color: #2d3748; margin-bottom: 20px;">Contract Signed Successfully</h2>
            
            <p>Dear <strong>{{recipientName}}</strong>,</p>
            
            <p>Great news! <strong>{{signerName}}</strong> has successfully signed the contract. The signature has been recorded on the blockchain and is now immutable.</p>
            
            <div class="success-badge">
                🎉 New Signature Recorded
            </div>
            
            <div class="status-update">
                <h3>📋 Contract Update</h3>
                <div class="detail-item">
                    <span class="detail-label">Contract:</span>
                    <span class="detail-value"><strong>{{contractTitle}}</strong></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Contract ID:</span>
                    <span class="detail-value">{{contractId}}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Signed by:</span>
                    <span class="detail-value">{{signerName}}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Status:</span>
                    <span class="detail-value">Partially Signed</span>
                </div>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{contractUrl}}" class="cta-button">
                    📊 View Contract Status
                </a>
            </div>
            
            <p><strong>What's next?</strong></p>
            <ul>
                <li>The contract is now partially signed and recorded on the blockchain</li>
                <li>Remaining parties will be notified to review and sign</li>
                <li>You'll receive another notification when all parties have signed</li>
                <li>Once fully signed, the contract becomes legally binding and complete</li>
            </ul>
            
            <div style="background: #edf2f7; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <p style="margin: 0; color: #4a5568; font-size: 14px;">
                    <strong>🔗 Blockchain Verification:</strong> This signature is permanently recorded on the Solana blockchain, 
                    providing immutable proof of agreement and ensuring the highest level of security and transparency.
                </p>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>Digital Contract Platform</strong></p>
            <p>Powered by Solana Blockchain • Secure • Transparent • Immutable</p>
            <p style="margin-top: 15px; font-size: 12px;">
                You received this notification because you are a party to this contract.
            </p>
        </div>
    </div>
</body>
</html>
